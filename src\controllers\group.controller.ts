import { FastifyInstance } from "fastify";
import { z } from "zod";
import { connection } from "../database/connection";
import { Group } from "../database/entities/Group.entity";

export function groupController(app: FastifyInstance, opts: any, done: () => void) {

  app.get("/list", async (request, reply) => {
    const { key } = z.object({
      key: z.string(),
    }).parse(request.query);
    const dataSource = await connection();
    // Optimized SQL query to fetch all required data in a single operation
    const groups: {
      id: number;
      name: string;
      createdAt: Date;
      profile_count: number;
      profile_countries: string;
      test_ids: string;
    }[] = await dataSource.query(`
      -- We start with the main 'groups' table to get basic group info.
      SELECT
        g.id,
        g.name,
        g.createdAt,
        -- For each group, we use subqueries to fetch additional data:
        -- Subquery to count profiles for each group (using profiles_groups join table)
        (SELECT COUNT(*) FROM profiles_groups pg WHERE pg.groupId = g.id) as profile_count,
        -- Subquery to get unique, comma-separated countries for each group (using profile and profiles_groups tables)
        -- We use GROUP_CONCAT to concatenate countries and test IDs, as STRING_AGG is not available in MySQL 5.7.
        COALESCE(
          (SELECT GROUP_CONCAT(DISTINCT p.country ORDER BY p.country SEPARATOR ', ')
           FROM profile p
           JOIN profiles_groups pg ON p.id = pg.profileId
           WHERE pg.groupId = g.id),
          ''
        ) as profile_countries,
        -- Subquery to get comma-separated test IDs for each group (using tests and tests_groups tables)
        COALESCE(
          (SELECT GROUP_CONCAT(t.id ORDER BY t.id SEPARATOR ', ')
           FROM tests t
           JOIN tests_groups tg ON t.id = tg.testId
           WHERE tg.groupId = g.id),
          ''
        ) as test_ids
      FROM \`groups\` g
      -- The result is ordered by creation date descending
      ORDER BY g.createdAt DESC
    `);

    return reply.view("/admin/group/list", {
      title: "All Groups",
      groups,
      url: request.url,
      key,
    });
  });

  app.post("/create", async (request, reply) => {
    try {
      const dataSource = await connection();
      const groupRepo = dataSource.getRepository(Group);
      const { group_id: id, group_name: name } = z.object({
        group_id: z.coerce.number(),
        group_name: z.string(),
      }).parse(request.body);
      const newGroup = groupRepo.create({ id, name });
      await groupRepo.insert(newGroup);
      return reply.status(200).send({ success: true, message: "Group created successfully" });
    } catch (error) {
      console.error("Error creating group:", error);
      return reply.status(400).send({
        success: false,
        message: error instanceof Error ? error.message : "An error occurred while creating the group"
      });
    }
  });

  app.get("/test_result", async (request, reply) => {
    const result = z.object({
      group_id: z.coerce.number(),
      sort: z.enum(['date', 'pass', 'date,pass', 'pass,date']),
      test: z.coerce.number().optional(),
      key: z.string(),
    }).safeParse(request.query);

    let groupId: number;
    let validSort: string;
    let orderBy: string;
    let testId: number | undefined;
    let key: string;

    if (result.success) {
      groupId = result.data.group_id;
      validSort = result.data.sort;
      testId = result.data.test;
      key = result.data.key;
    } else if (result.error.issues.some(issue => issue.path.includes('sort'))) {
      const parsedQuery = z.object({
        group_id: z.coerce.number(),
        test: z.coerce.number().optional(),
        key: z.string(),
      }).parse(request.query);
      groupId = parsedQuery.group_id;
      testId = parsedQuery.test;
      key = parsedQuery.key;
      // Redirect to the test result page with the default sort order
      return reply.redirect(`/admin/group/test_result?group_id=${groupId}&key=${key}&sort=date,pass${testId ? `&test_id=${testId}` : ''}`);
    } else {

      throw result.error;
    }

    // SQL query to get the test results with proper sorting.
    switch(validSort) {
      case 'date':
        orderBy = 'completion_date DESC';
        break;
      case 'pass':
        orderBy = 'correct_answers DESC';
        break;
      case 'pass,date':
        orderBy = 'correct_answers DESC, completion_date DESC';
        break;
      default:
        orderBy = 'completion_date DESC, correct_answers DESC';
    }

    const dataSource = await connection();
    const rawTestResults: {
      user_link_id: string;
      user_name: string;
      country: string;
      correct_answers: number;
      total_answers: number;
      question_details_raw: string;
      completion_date: Date;
      test_names: string;
    }[] = await dataSource.query(`
      SELECT
        -- Basic user information
        p.link AS user_link_id,
        p.name AS user_name,
        p.country,
        -- Count of correct answers for this user in this group (only for tests belonging to the group)
        COUNT(CASE WHEN ta.is_correct = 1 THEN 1 END) AS correct_answers,
        -- Total number of answers for this user in this group (only for tests belonging to the group)
        COUNT(ta.id) AS total_answers,
        -- Detailed information about each question answered, sorted by test_id and priority
        GROUP_CONCAT(
          CONCAT(
            '{"answer_string":"',
            CONCAT(
              ta.question_id,
              '(',
              ta.time_taken,
              ',',
              ta.inactive_time,
              ',',
              ta.copy_count + ta.paste_count + ta.right_click_count,
              ')'
            ),
            '","isCorrect":',
            CASE WHEN ta.is_correct = 1 THEN 'true' ELSE 'false' END,
            ',"test_id":',
            ta.test_id,
            '}'
          )
          ORDER BY ta.test_id, qt.priority
          SEPARATOR ','
        ) AS question_details_raw,
        -- The latest answer date, considered as the completion date
        MAX(ta.created_at) AS completion_date,
        -- Collect unique test names for each profile (only tests belonging to the group)
        GROUP_CONCAT(
            DISTINCT t.name
            ORDER BY t.name
            SEPARATOR ', '
        ) AS test_names
      FROM profile p
      -- Join to get all profiles in the specified group
      JOIN profiles_groups pg ON p.id = pg.profileId
      -- Join to get all answers for these profiles
      JOIN answers ta ON p.id = ta.profile_id
      -- Join with tests to get test details and ensure test exists
      JOIN tests t ON ta.test_id = t.id
      -- Join to ensure only tests belonging to the group are included
      INNER JOIN tests_groups tg ON t.id = tg.testId AND tg.groupId = pg.groupId
      -- Join with questions to ensure question exists (filter out orphaned answers)
      JOIN questions q ON ta.question_id = q.id
      -- Join with questions_tests to get priority information and ensure question-test relationship exists
      JOIN questions_tests qt ON ta.question_id = qt.question_id AND ta.test_id = qt.test_id
      -- Filter for the specific group
      WHERE pg.groupId = ?
        AND q.id IS NOT NULL
        ${testId ? 'AND ta.test_id = ?' : ''}
      -- Group results by user
      GROUP BY p.id, p.name, p.country
      ORDER BY ${orderBy}
    `, testId ? [groupId, testId] : [groupId]);

    // Process the raw results to convert the concatenated JSON string back to an array
    const testResults = rawTestResults.map(result => ({
      ...result,
      question_details: result.question_details_raw
        ? JSON.parse(`[${result.question_details_raw}]`)
        : []
    }));

    return reply.view("/admin/group/test_result", {
      title: "Test Results",
      testResults,
      url: request.url,
      currentSort: validSort,
      groupId,
      testId,
      key,
    });
  });

  app.get("/detailed_result", async (request, reply) => {
    const { group_id, test: test_id, key } = z.object({
      group_id: z.coerce.number(),
      test: z.coerce.number().optional(),
      key: z.string(),
    }).parse(request.query);

    const dataSource = await connection();
    const detailedResults: {
      profile_id: number;
      profile_name: string;
      country: string;
      hourly_rate: number;
      link_id: string;
      url: string | null;
      last_answer_date: Date;
      answer_details: string;
      test_names: string;
    }[] = await dataSource.query(`
      -- Start with selecting from the groups table
      SELECT
          p.id AS profile_id,
          p.name AS profile_name,
          p.country,
          p.rate AS hourly_rate,
          p.link AS link_id,
          p.url AS url,
          -- Get the most recent answer date for each profile
          MAX(a.created_at) AS last_answer_date,
          -- Collect all answers for each profile
          GROUP_CONCAT(
              CONCAT(
                  IFNULL(a.id, ''), '::', -- Answer ID
                  IFNULL(t.id, ''), '::', -- Test ID
                  IFNULL(q.id, ''), '::', -- Question ID
                  IFNULL(q.question, ''), '::', -- Question text
                  IFNULL(a.answer, ''), '::', -- Answer text
                  IFNULL(a.time_taken, ''), '::', -- Time taken
                  IFNULL(a.paste_count, ''), '::', -- Ctrl+V count
                  IFNULL(a.copy_count, ''), '::', -- Ctrl+C count
                  IFNULL(a.right_click_count, ''), '::', -- Right-click count
                  IFNULL(a.inactive_time, ''), "::", -- Inactive time
                  IFNULL(a.is_correct, '')
              )
              ORDER BY a.created_at DESC
              SEPARATOR '||'
          ) AS answer_details,
          -- Collect unique test names for each profile
          GROUP_CONCAT(
              DISTINCT t.name
              ORDER BY t.name
              SEPARATOR ', '
          ) AS test_names
      FROM
          \`groups\` g
      -- Join with profiles_groups to get all profiles in the group
      INNER JOIN profiles_groups pg ON g.id = pg.groupId
      -- Join with profiles to get profile details
      INNER JOIN profile p ON pg.profileId = p.id
      -- Inner join with answers to exclude profiles without answers
      INNER JOIN answers a ON p.id = a.profile_id
      -- Join with questions to get question details
      LEFT JOIN questions q ON a.question_id = q.id
      -- Join with tests to get test details
      LEFT JOIN tests t ON a.test_id = t.id
      WHERE
          g.id = ? -- Parameter for group ID
          ${test_id ? 'AND t.id = ?' : ''}
      GROUP BY
          p.id
      ORDER BY
          last_answer_date DESC
    `, test_id ? [group_id, test_id] : [group_id]);

    // Process the results
    const processedResults = detailedResults.map(result => {
      return {
        profile: {
          id: result.profile_id,
          name: result.profile_name,
          country: result.country,
          hourlyRate: result.hourly_rate,
          linkId: result.link_id,
          lastAnswerDate: new Date(result.last_answer_date).toLocaleString(),
          url: result.url,
        },
        testNames: result.test_names, // Add test names to the result
        answers: result.answer_details.split('||').map(answer => {
          const [id, testId, questionId, question, answerText, timeTaken, ctrlV, ctrlC, rightClick, inactive, isCorrect] = answer.split('::');
          return {
            id: Number(id),
            testId: Number(testId),
            questionId: Number(questionId),
            question,
            answer: answerText,
            timeTaken: Number(timeTaken),
            inactive: Number(inactive),
            copyPaste: Number(ctrlV) + Number(ctrlC) + Number(rightClick),
            isCorrect: isCorrect === '1' ? true : false,
          };
        }),
      };
    });

    return reply.view("/admin/group/detailed_result", {
      title: "Detailed Test Results",
      detailedResults: processedResults,
      url: request.url,
      groupId: group_id,
      testId: test_id, // Pass the test_id to the template
      key,
    });
  });

  done()
}